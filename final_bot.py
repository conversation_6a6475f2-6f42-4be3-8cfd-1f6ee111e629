#!/usr/bin/env python3
"""
Genesis Telegram Referral Bot
Simplified version focused on core referral functionality
"""

import logging
import asyncio
import signal
import sys
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters, ContextTypes

from config import Config
from src.database import Database
from src.models.user import User
from src.models.transaction import Transaction, TransactionType, TransactionStatus
from src.models.referral import Referral, ReferralStatus
from src.services.user_service import UserService
from src.services.referral_service import ReferralService
from src.services.transaction_service import TransactionService
from src.utils.logger import setup_logger, setup_production_logging
from src.utils.security import rate_limiter, ban_manager

# Setup logging
logger = setup_logger(__name__)
startup_logger = setup_production_logging()

class GenesisBotApp:
    """Genesis Telegram Referral Bot Application"""
    
    def __init__(self):
        self.application = None
        self.database = None
        self.services = {}

        # Shutdown management
        self.shutdown_event = asyncio.Event()
        self.shutdown_in_progress = False
        
    async def initialize_async_components(self):
        """Initialize async components"""
        try:
            startup_logger.info("🤖 Starting Genesis Telegram Referral Bot...")
            startup_logger.info("🔄 Mode: Long Polling (no webhook/domain required)")
            
            # Validate configuration
            Config.validate_config()
            logger.info("✅ Configuration validated")
            
            # Initialize database connection
            startup_logger.info("🔄 Initializing database connection...")
            self.database = Database()
            db_connected = await self.database.connect()

            if db_connected:
                startup_logger.info("✅ Database connected successfully")
            else:
                startup_logger.error("❌ Database connection failed")
                raise Exception("Database connection failed")
            
            # Initialize simplified services
            startup_logger.info("🔄 Initializing simplified services...")
            self.services = {
                'user': UserService(self.database),
                'referral': ReferralService(self.database),
                'transaction': TransactionService(self.database)
            }
            startup_logger.info("✅ Services initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize async components: {e}")
            raise

    def get_main_keyboard(self):
        """Get main menu keyboard with 4-button layout"""
        keyboard = [
            [KeyboardButton("Earn Genesis Token"), KeyboardButton("Genesis Token Balance")],
            [KeyboardButton("Top 5 Users"), KeyboardButton("About Genesis Bot")]
        ]
        return ReplyKeyboardMarkup(
            keyboard,
            resize_keyboard=True,
            one_time_keyboard=False,
            input_field_placeholder="Choose your option! 🎁"
        )
    
    async def check_user_permissions(self, user_id: int) -> dict:
        """Check user permissions and restrictions"""
        result = {
            'allowed': True,
            'reason': '',
            'banned': False,
            'rate_limited': False
        }
        
        # Check if user is banned
        try:
            user = await self.services['user'].get_user(user_id)
            if user and user.is_banned:
                result['allowed'] = False
                result['banned'] = True
                result['reason'] = user.ban_reason or "You are banned from using this bot."
                return result
        except:
            pass  # Continue if user service fails
        
        # Check temporary ban
        if ban_manager.is_temp_banned(user_id):
            remaining = ban_manager.get_ban_time_remaining(user_id)
            result['allowed'] = False
            result['banned'] = True
            result['reason'] = f"You are temporarily banned. Time remaining: {remaining}"
            return result
        
        # Check rate limiting
        if rate_limiter.is_rate_limited(user_id):
            result['allowed'] = False
            result['rate_limited'] = True
            result['reason'] = "You are sending messages too quickly. Please slow down."
            return result
        
        return result
    
    def extract_referral_code(self, text: str):
        """Extract referral code from start command"""
        if not text or not text.startswith('/start'):
            return None
        
        parts = text.split()
        if len(parts) > 1:
            code = parts[1].strip()
            if len(code) == 8 and code.isalnum():
                return code.upper()
        
        return None
    
    # ==================== COMMAND HANDLERS ====================
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command with enhanced referral flow"""
        try:
            user = update.effective_user

            # Check user permissions
            permissions = await self.check_user_permissions(user.id)
            if not permissions['allowed']:
                try:
                    await update.message.reply_text(permissions['reason'])
                except Exception:
                    pass
                return

            # Store user data immediately
            referral_code = None
            if context.args:
                referral_code = self.extract_referral_code(f"/start {context.args[0]}")

            # Create user data structure
            user_data = {
                'user_id': user.id,
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'language_code': user.language_code,
                'is_bot': user.is_bot,
                'is_premium': getattr(user, 'is_premium', False)
            }

            # Store user immediately
            try:
                logger.debug(f"Storing user data for user {user.id}")
                if referral_code:
                    logger.debug(f"Referral code provided: {referral_code}")

                db_user = await self.services['user'].create_user(user_data, referral_code)

                if db_user:
                    logger.debug(f"User data stored for user {user.id}")
                else:
                    logger.error(f"❌ FAILED: create_user returned None for user {user.id}")

            except Exception as e:
                logger.error(f"❌ EXCEPTION: Failed to store user data for user {user.id}: {e}")
                db_user = None

            # Perform channel membership verification
            logger.debug(f"Verifying channel membership for user {user.id}")
            is_member = await self._verify_required_channels(user.id, context)

            if is_member:
                # User is member of the required channel
                await self._update_user_channel_status(user.id, True)

                # Process referral after channel verification
                if referral_code and db_user and db_user.referred_by:
                    await self._process_referral(db_user.user_id, db_user.referred_by, referral_code)

                # Show welcome
                await self._show_welcome_response(update, context, user, db_user, referral_code)
            else:
                # User is not member of required channels
                await self._update_user_channel_status(user.id, False)

                # Store referral code for processing after channel join
                if referral_code:
                    context.user_data['pending_referral'] = referral_code
                    context.user_data['pending_referrer'] = db_user.referred_by if db_user else None

                # Show channel join interface
                await self._show_channel_join_interface(update, context, user)

        except Exception as e:
            # Handle bot blocked scenarios silently
            error_msg = str(e).lower()
            if "forbidden" in error_msg and "bot was blocked" in error_msg:
                logger.debug(f"User {user.id} has blocked the bot")
                return

            logger.error(f"Error in start command: {e}")
            try:
                await update.message.reply_text("❌ An error occurred. Please try again.")
            except Exception:
                pass

    async def _verify_required_channels(self, user_id: int, context: ContextTypes.DEFAULT_TYPE) -> bool:
        """Verify if user is member of the required channel"""
        try:
            channel_id = Config.get_required_channel_id()
            logger.debug(f"Starting channel verification for user {user_id}")

            try:
                logger.info(f"Checking membership in channel: {channel_id}")
                member = await context.bot.get_chat_member(chat_id=channel_id, user_id=user_id)
                logger.info(f"User {user_id} status in channel {channel_id}: {member.status}")

                valid_statuses = ['member', 'administrator', 'creator']
                invalid_statuses = ['left', 'kicked', 'restricted']

                if member.status in invalid_statuses:
                    logger.info(f"❌ User {user_id} rejected - invalid status in channel {channel_id}: {member.status}")
                    return False

                if member.status in valid_statuses:
                    logger.info(f"✅ User {user_id} accepted - valid status in channel {channel_id}: {member.status}")
                    return True
                else:
                    logger.warning(f"⚠️ User {user_id} has unknown status in channel {channel_id}: {member.status}")
                    return False

            except Exception as e:
                error_msg = str(e).lower()
                if "chat not found" in error_msg:
                    logger.error(f"Channel {channel_id} not found - bot may not be admin or channel ID incorrect")
                elif "user not found" in error_msg:
                    logger.error(f"User {user_id} not found in channel {channel_id}")
                elif "forbidden" in error_msg:
                    logger.error(f"Bot lacks permission to check membership in channel {channel_id}")
                else:
                    logger.error(f"Unexpected error checking membership for user {user_id} in channel {channel_id}: {e}")
                return False

        except Exception as e:
            logger.error(f"Critical error in channel membership verification: {e}")
            return False

    async def _update_user_channel_status(self, user_id: int, has_joined: bool):
        """Update user's channel join status in database"""
        try:
            user = await self.services['user'].get_user(user_id)
            if user:
                user.has_joined_channels = has_joined
                await self.services['user'].update_user(user)
                logger.debug(f"Updated channel status for user {user_id}: has_joined_channels = {has_joined}")
        except Exception as e:
            logger.error(f"Failed to update channel status for user {user_id}: {e}")

    async def _process_referral(self, user_id: int, referrer_id: int, referral_code: str):
        """Process referral and award bonus"""
        try:
            # Create referral record
            referral = await self.services['referral'].create_referral(referrer_id, user_id, referral_code)
            if referral:
                # Complete the referral and award bonus
                success = await self.services['referral'].complete_referral(referral.referral_id)
                if success:
                    logger.info(f"✅ Referral processed: {referrer_id} -> {user_id}")
                else:
                    logger.error(f"❌ Failed to complete referral: {referrer_id} -> {user_id}")
            else:
                logger.error(f"❌ Failed to create referral: {referrer_id} -> {user_id}")
        except Exception as e:
            logger.error(f"Error processing referral: {e}")

    async def _show_welcome_response(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user, db_user, referral_code):
        """Show welcome response"""
        try:
            welcome_message = f"""
🎉 **Welcome to Genesis Bot!** 🎉

Hello {user.first_name}! 👋

🪙 **Your Genesis Token Balance:** {int(db_user.balance) if db_user else 0} Genesis Tokens
🔗 **Your Referral Code:** `{db_user.referral_code if db_user else 'Loading...'}`

**How to Earn Genesis Tokens:**
• Share your referral link with friends
• Earn {Config.REFERRAL_REWARD} Genesis Tokens for each successful referral
• Your friends get {Config.FRIEND_WELCOME_BONUS} Genesis Tokens for joining!

Start earning now by inviting your friends! 🚀
            """

            try:
                with open('start.jpg', 'rb') as photo_file:
                    await context.bot.send_photo(
                        chat_id=update.effective_chat.id,
                        photo=photo_file,
                        caption=welcome_message,
                        parse_mode='Markdown',
                        reply_markup=self.get_main_keyboard()
                    )
            except FileNotFoundError:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=welcome_message,
                    parse_mode='Markdown',
                    reply_markup=self.get_main_keyboard()
                )

            # Mark registration as completed
            if db_user:
                await self.services['user'].mark_registration_completed(db_user.user_id)

        except Exception as e:
            logger.error(f"Error in welcome response: {e}")
            fallback_message = "🎁 Welcome to Genesis Bot! 🎉\n\nStart earning Genesis Tokens by inviting friends!"
            try:
                await update.message.reply_text(
                    fallback_message,
                    reply_markup=self.get_main_keyboard()
                )
            except:
                pass

    async def _show_channel_join_interface(self, update: Update, context: ContextTypes.DEFAULT_TYPE, user):
        """Show channel join interface"""
        try:
            join_caption = "Please join our channel to use the bot"
            invite_link = Config.get_channel_invite_link()

            keyboard = [
                [InlineKeyboardButton("📢 JOIN CHANNEL", url=invite_link)],
                [InlineKeyboardButton("✅ I HAVE JOINED", callback_data="verify_channels")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            try:
                with open('join.jpg', 'rb') as photo_file:
                    await context.bot.send_photo(
                        chat_id=update.effective_chat.id,
                        photo=photo_file,
                        caption=join_caption,
                        parse_mode='Markdown',
                        reply_markup=reply_markup
                    )
            except FileNotFoundError:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text=f"🔐 **Channel Verification Required**\n\n{join_caption}",
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )

        except Exception as e:
            logger.error(f"Error showing channel join interface: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle balance command"""
        try:
            user_id = update.effective_user.id
            telegram_user = update.effective_user

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user: {e}")
                user = None

            if not user:
                profile_text = """
👤 **Profile Not Found**

❌ *Please start the bot with /start to create your profile.*
                """
            else:
                # Format join date
                try:
                    if user.created_at and isinstance(user.created_at, datetime):
                        join_date = user.created_at.strftime("%d %b %Y")
                    else:
                        join_date = "Unknown"
                except (AttributeError, ValueError):
                    join_date = "Unknown"

                profile_text = f"""
╭─────────────────────────╮
│  ✦ **{user.get_display_name()}'s Genesis Profile** ✦  │
╰─────────────────────────╯

◆ *🪙 GENESIS TOKEN BALANCE* ◆

◉ *Balance:* *{int(user.balance)} Genesis Tokens*

═══════════════════════
🥫 *👥 REFERRAL STATS* 🥫

▪ **Referrals:** **{user.successful_referrals}** people
▪ **Earned:** *{int(user.successful_referrals * Config.REFERRAL_REWARD)} Genesis Tokens from referrals*
▪ **Your Code:** `{user.referral_code}`
═══════════════════════

✧ *Member since {join_date}* ✧
                """

            await update.message.reply_text(
                profile_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in balance command: {e}")
            await update.message.reply_text("❌ Failed to load profile information.")

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            user_id = update.effective_user.id

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            help_text = f"""
🎁 **GENESIS BOT GUIDE** 🎁

╭─────────────────────────╮
│  ✦ **GENESIS TOKEN EARNING SYSTEM** ✦  │
╰─────────────────────────╯

◆ *REFERRAL REWARDS* ◆
▪ **Per Referral:** **{Config.REFERRAL_REWARD} Genesis Tokens** per friend
▪ **Friend Bonus:** **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens** for joining with your link

═══════════════════════

✧ **HOW TO EARN GENESIS TOKENS** ✧
▪ **Share your referral link** with friends
▪ **Invite friends** - you get {Config.REFERRAL_REWARD} Genesis Tokens when they join!
▪ **Track progress** with your Genesis Token Balance
▪ **Compete** on the leaderboard for weekly prizes

🎯 **QUICK START GUIDE:**
1. **Get your referral link** from "Earn Genesis Token" button
2. **Share with friends** on social media, WhatsApp, X, etc.
3. **Earn {Config.REFERRAL_REWARD} Genesis Tokens** when friends join using your link
4. **Check leaderboard** to see your ranking

📱 **AVAILABLE FEATURES:**
• **🪙 Genesis Token Balance** - Check your current balance and stats
• **🪙 Earn Genesis Token** - Get your referral link and view stats
• **🏆 Top 5 Users** - View leaderboard and your ranking
• **ℹ️ About Genesis Bot** - Learn about the bot and rewards system

🔗 **REFERRAL SYSTEM:**
Your unique referral code helps track your earnings. When someone joins using your link:
• You earn **{Config.REFERRAL_REWARD} Genesis Tokens** instantly
• Your friend gets **{Config.FRIEND_WELCOME_BONUS} Genesis Tokens** for joining
• Both accounts are credited automatically

🏆 **LEADERBOARD & REWARDS:**
• Top 5 users each week win guaranteed prizes
• Weekly giveaways for all active users
• Climb the leaderboard by earning more Genesis Tokens

💡 **PRO TIPS:**
• Share your link in multiple places for maximum reach
• Engage with friends to encourage them to join
• Check your balance and ranking regularly
• Participate in weekly competitions

❓ **Need Help?**
Genesis Bot - Your gateway to airdrops, giveaways, and exclusive rewards! 🌟
            """

            await update.message.reply_text(
                help_text,
                parse_mode='Markdown',
                reply_markup=self.get_main_keyboard()
            )

        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        try:
            # Skip channel posts and group messages
            if update.channel_post or update.edited_channel_post:
                return

            if update.message and update.message.chat.type in ['group', 'supergroup']:
                return

            if not update.effective_user or not update.message or not update.message.text:
                return

            user_id = update.effective_user.id
            message_text = update.message.text

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Handle main menu buttons
            if message_text == "Genesis Token Balance":
                await self.balance_command(update, context)
            elif message_text == "Earn Genesis Token":
                await self._handle_referrals_menu(update, context)
            elif message_text == "Top 5 Users":
                await self._handle_top_users(update, context)
            elif message_text == "About Genesis Bot":
                await self._handle_about_bot(update, context)
            elif message_text == "🔙 MAIN MENU":
                await update.message.reply_text(
                    "🏠 **Returning to main menu...**",
                    reply_markup=self.get_main_keyboard(),
                    parse_mode='Markdown'
                )
            else:
                # Default message for unrecognized input
                await update.message.reply_text(
                    "🎁 Use the buttons below to navigate! ✨",
                    reply_markup=self.get_main_keyboard()
                )

        except Exception as e:
            logger.error(f"Error handling message: {e}")
            await update.message.reply_text("❌ An error occurred. Please try again.")

    async def _handle_referrals_menu(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle referrals menu"""
        try:
            user_id = update.effective_user.id

            try:
                user = await self.services['user'].get_user(user_id)
            except Exception as e:
                logger.error(f"Failed to get user for referrals: {e}")
                user = None

            if not user:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start=REF{user_id}"
                referral_count = 0
                referral_earnings = 0.0
            else:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
                try:
                    referral_count = await self.services['referral'].get_successful_referral_count(user_id)
                except:
                    referral_count = user.successful_referrals

                referral_earnings = referral_count * Config.REFERRAL_REWARD

            referral_text = f"""
🪙 **EARN GENESIS TOKENS** 🪙

╭─────────────────────────╮
│  ✦ **YOUR REFERRAL STATS** ✦  │
╰─────────────────────────╯

◆ *📊 PERFORMANCE* ◆

◉ **Total Referrals:** {referral_count} people
◉ **Total Earned:** {int(referral_earnings)} Genesis Tokens
◉ **Per Referral:** {Config.REFERRAL_REWARD} Genesis Tokens

═══════════════════════

🔗 **Your Referral Link:**
`{referral_link}`

💡 **How to Earn Genesis Tokens:**
1. Share your link with friends
2. They join using your link
3. You earn {Config.REFERRAL_REWARD} Genesis Tokens per referral!
4. Your friend gets {Config.FRIEND_WELCOME_BONUS} Genesis Tokens for joining!

🚀 **Share on:**
• WhatsApp, Telegram groups
• Social media platforms
• With friends and family
            """

            keyboard = [
                [InlineKeyboardButton("📋 Copy Link", callback_data="copy_referral_link")],
                [InlineKeyboardButton("📊 Referral Stats", callback_data="referral_stats")],
                [InlineKeyboardButton("🔙 Main Menu", callback_data="main_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                referral_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in referrals menu: {e}")
            await update.message.reply_text("❌ Failed to load referral information.")

    async def _handle_top_users(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle Top 5 Users leaderboard"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            # Get top 5 users by balance
            try:
                # Query database for top users
                top_users_cursor = self.database.db.users.find(
                    {"balance": {"$gt": 0}},
                    {"user_id": 1, "first_name": 1, "balance": 1}
                ).sort("balance", -1).limit(5)

                top_users = await top_users_cursor.to_list(length=5)

                # Get current user's rank
                current_user = await self.services['user'].get_user(user_id)
                if current_user:
                    # Count users with higher balance
                    higher_balance_count = await self.database.db.users.count_documents(
                        {"balance": {"$gt": current_user.balance}}
                    )
                    current_user_rank = higher_balance_count + 1
                else:
                    current_user_rank = "N/A"

            except Exception as e:
                logger.error(f"Failed to get leaderboard data: {e}")
                await update.message.reply_text("❌ Failed to load leaderboard.")
                return

            # Format leaderboard message
            leaderboard_text = """
🏆 **TOP 5 GENESIS TOKEN HOLDERS** 🏆

╭─────────────────────────╮
│  ✦ **LEADERBOARD** ✦  │
╰─────────────────────────╯

"""

            if top_users:
                for i, user in enumerate(top_users, 1):
                    first_name = user.get('first_name', 'Anonymous')
                    balance = int(user.get('balance', 0))

                    # Add medal emojis for top 3
                    if i == 1:
                        medal = "🥇"
                    elif i == 2:
                        medal = "🥈"
                    elif i == 3:
                        medal = "🥉"
                    else:
                        medal = f"{i}."

                    leaderboard_text += f"{medal} **{first_name}** - {balance:,} Genesis Tokens\n"
            else:
                leaderboard_text += "No users with Genesis Tokens yet.\nBe the first to earn some!\n"

            leaderboard_text += f"""
═══════════════════════

📍 **Your Rank:** #{current_user_rank}
🪙 **Your Balance:** {int(current_user.balance) if current_user else 0} Genesis Tokens

💡 **Climb the leaderboard by earning more Genesis Tokens!**
Share your referral link to earn {Config.REFERRAL_REWARD} tokens per friend.
            """

            await update.message.reply_text(
                leaderboard_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in top users handler: {e}")
            await update.message.reply_text("❌ Failed to load leaderboard.")

    async def _handle_about_bot(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle About Genesis Bot information"""
        try:
            user_id = update.effective_user.id

            # Check permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await update.message.reply_text(permissions['reason'])
                return

            about_text = """
🚀 Introducing Genesis Whales Bot
Powered by Genesis Crypto Whales 🐋

Your gateway to airdrops, giveaways, and exclusive rewards starts here.

⸻

🎁 What Is It?

Genesis Whales Bot is an official rewards system created to distribute airdrops, gifts, and weekly prizes to our community.

✅ Invite friends
✅ Earn Genesis Tokens
✅ Climb the leaderboard
✅ Win guaranteed rewards

⸻

🪙 How It Works

Every friend you refer = 50 Genesis Tokens for you
Your friend also gets 25 Genesis Tokens for joining with your link!

🔝 The Top 5 users on the leaderboard each week win guaranteed prizes
🎉 Plus, a weekly giveaway for all active users – everyone gets a shot

⸻

📝 Steps to Start Earning
 1. Tap "Earn Genesis Tokens" in the bot
 2. Copy your unique referral link
 3. Share it with your network (Telegram, WhatsApp, X, anywhere)
 4. Earn tokens as your friends join through your link
 5. Track your rank in the Leaderboard tab

⸻

🎯 The more you share, the more you earn.
Start stacking Genesis Tokens and climb your way to exclusive drops 💸

👉 Ready to begin?
            """

            await update.message.reply_text(
                about_text,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"Error in about bot handler: {e}")
            await update.message.reply_text("❌ Failed to load information.")

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle callback queries"""
        try:
            query = update.callback_query
            await query.answer()

            user_id = query.from_user.id
            data = query.data

            # Check user permissions
            permissions = await self.check_user_permissions(user_id)
            if not permissions['allowed']:
                await query.edit_message_text(permissions['reason'])
                return

            if data == "verify_channels":
                await self._handle_channel_verification_callback(query, context)
            elif data == "copy_referral_link":
                await self._copy_referral_link(query, context)
            elif data == "referral_stats":
                await self._show_referral_stats(query, context)
            elif data == "main_menu":
                await query.edit_message_text(
                    "🏠 **Main Menu**\n\nUse the buttons below to navigate:",
                    parse_mode='Markdown'
                )
            else:
                await query.edit_message_text("❌ Unknown action.")

        except Exception as e:
            logger.error(f"Error in callback handler: {e}")
            try:
                await query.answer("❌ An error occurred.")
            except:
                pass

    async def _handle_channel_verification_callback(self, query, context):
        """Handle channel verification callback"""
        try:
            user_id = query.from_user.id

            # Verify channel membership
            is_member = await self._verify_required_channels(user_id, context)

            if is_member:
                # Update user status
                await self._update_user_channel_status(user_id, True)

                # Process pending referral if any
                if 'pending_referral' in context.user_data and 'pending_referrer' in context.user_data:
                    referral_code = context.user_data['pending_referral']
                    referrer_id = context.user_data['pending_referrer']

                    if referrer_id:
                        await self._process_referral(user_id, referrer_id, referral_code)

                    # Clear pending data
                    del context.user_data['pending_referral']
                    del context.user_data['pending_referrer']

                # Get user data for welcome
                user = await self.services['user'].get_user(user_id)

                welcome_message = f"""
✅ **Verification Successful!**

Welcome to Genesis Bot! 🎉

🪙 **Your Balance:** {int(user.balance) if user else 0} Genesis Tokens
🔗 **Your Referral Code:** `{user.referral_code if user else 'Loading...'}`

Start earning Genesis Tokens by sharing your referral link!
                """

                await query.edit_message_text(
                    welcome_message,
                    parse_mode='Markdown'
                )

                # Mark registration as completed
                if user:
                    await self.services['user'].mark_registration_completed(user.user_id)

            else:
                await query.edit_message_text(
                    "❌ **Verification Failed**\n\nPlease make sure you have joined the channel and try again.",
                    parse_mode='Markdown',
                    reply_markup=query.message.reply_markup
                )

        except Exception as e:
            logger.error(f"Error in channel verification callback: {e}")
            await query.edit_message_text("❌ Verification failed. Please try again.")

    async def _copy_referral_link(self, query, context):
        """Handle copy referral link callback"""
        try:
            user_id = query.from_user.id
            user = await self.services['user'].get_user(user_id)

            if user:
                referral_link = f"https://t.me/{Config.BOT_USERNAME}?start={user.referral_code}"
                await query.edit_message_text(
                    f"📋 **Your Referral Link:**\n\n`{referral_link}`\n\n*Tap to copy and share with friends!*",
                    parse_mode='Markdown'
                )
            else:
                await query.edit_message_text("❌ Failed to get your referral link.")

        except Exception as e:
            logger.error(f"Error copying referral link: {e}")
            await query.edit_message_text("❌ Failed to copy referral link.")

    async def _show_referral_stats(self, query, context):
        """Show detailed referral statistics"""
        try:
            user_id = query.from_user.id
            user = await self.services['user'].get_user(user_id)

            if user:
                referral_count = user.successful_referrals
                total_earned = referral_count * Config.REFERRAL_REWARD

                stats_text = f"""
📊 **DETAILED REFERRAL STATS** 📊

👥 **Total Referrals:** {referral_count}
🪙 **Total Earned:** {int(total_earned)} Genesis Tokens
💰 **Per Referral:** {Config.REFERRAL_REWARD} Genesis Tokens

🔗 **Your Code:** `{user.referral_code}`

Keep sharing to earn more Genesis Tokens! 🚀
                """

                keyboard = [[InlineKeyboardButton("🔙 Back", callback_data="main_menu")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_text(
                    stats_text,
                    parse_mode='Markdown',
                    reply_markup=reply_markup
                )
            else:
                await query.edit_message_text("❌ Failed to load referral statistics.")

        except Exception as e:
            logger.error(f"Error showing referral stats: {e}")
            await query.edit_message_text("❌ Failed to load statistics.")

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle errors"""
        logger.error(f"Exception while handling an update: {context.error}")

    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("🔄 Shutting down bot...")
        self.shutdown_in_progress = True
        self.shutdown_event.set()

        if self.database:
            await self.database.close()

        logger.info("✅ Bot shutdown complete")

# ==================== MAIN FUNCTION ====================

async def main():
    """Main function to run the bot"""
    bot = GenesisBotApp()

    try:
        # Create application
        application = Application.builder().token(Config.BOT_TOKEN).build()
        bot.application = application

        # Initialize async components
        await bot.initialize_async_components()

        # Add handlers
        application.add_handler(CommandHandler("start", bot.start_command))
        application.add_handler(CommandHandler("balance", bot.balance_command))
        application.add_handler(CommandHandler("help", bot.help_command))

        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND & filters.ChatType.PRIVATE, bot.handle_message))
        application.add_handler(CallbackQueryHandler(bot.handle_callback))
        application.add_error_handler(bot.error_handler)

        startup_logger.info("✅ All handlers added successfully")

        # Start the bot manually to avoid event loop issues
        startup_logger.info("🚀 Starting bot in long polling mode...")

        # Initialize and start the application manually
        await application.initialize()
        await application.start()

        # Start the updater
        await application.updater.start_polling(drop_pending_updates=True)

        # Keep the bot running
        try:
            # Create a simple event to keep the bot running
            stop_event = asyncio.Event()

            # Set up signal handlers
            import signal
            def signal_handler(signum, frame):
                logger.info(f"Received signal {signum}, stopping bot...")
                stop_event.set()

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            # Wait for stop signal
            await stop_event.wait()

        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, stopping bot...")
        finally:
            # Clean shutdown
            await application.updater.stop()
            await application.stop()
            await application.shutdown()

    except Exception as e:
        logger.error(f"❌ Critical error in main: {e}")
        raise

if __name__ == '__main__':
    try:
        # Use asyncio.run() to handle the event loop properly
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
